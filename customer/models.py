import uuid
import random
import string
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy
from django.conf import settings
# from user.models import User
from linechatbot.models import LineUserProfile
from devproject.utils.azure_storage import AzureBlobStorage
from django.core.validators import RegexValidator

class Gender(models.Model):
    # If change primary key's name, change it at List view too
    gender_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    definition = models.Char<PERSON>ield(max_length=250)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='gender_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='gender_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def delete(self):
        self.is_active = False
        self.save()

    def __str__(self):
        id = self.gender_id
        name = self.name
        string = f"{id}: {name}"
        return string

class Interface(models.Model):

    class InterfaceType(models.TextChoices):
        NONE = "NONE", gettext_lazy("None")
        CALLING = "CALLING", gettext_lazy("Calling")
        LINE = "LINE", gettext_lazy("Line")
        FBMESSENGER = "FBMESSENGER", gettext_lazy("FBMessenger")

    name = models.CharField(max_length=20, unique=True, choices=InterfaceType.choices)
    definition = models.CharField(max_length=250)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='interface_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='interface_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def delete(self):
        self.is_active = False
        self.save()

    def __str__(self):
        id = self.id
        name = self.name
        string = f"Interface {id}: {name}"
        return string

class CustomerTag(models.Model):
    name = models.CharField(max_length=100, unique=True)
    # code = models.CharField(max_length=50, unique=True, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(max_length=100, default="grey") # Default color
    # is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customertag_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customertag_updated_by',
        blank=True,
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        id = self.pk
        name = self.name
        return f"id: {id}, name: {name}"

class Customer(models.Model):
    """
    Enhanced Customer model with comprehensive fields for multi-channel support
    This is the universal customer profile that can have multiple platform identities.
    """
    customer_id = models.AutoField(primary_key=True)
    universal_id = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    
    # ========== PERSONAL INFORMATION ==========
    # Name fields
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    middle_name = models.CharField(max_length=100, null=True, blank=True)
    name = models.CharField(max_length=255, null=True, blank=True)  # Full name or display name
    nickname = models.CharField(max_length=50, null=True, blank=True)
    
    # Title and honorifics
    title = models.CharField(
        max_length=20,
        choices=[
            ('MR', 'Mr.'),
            ('MRS', 'Mrs.'),
            ('MS', 'Ms.'),
            ('DR', 'Dr.'),
            ('PROF', 'Prof.'),
            ('KHUN', 'คุณ'),
        ],
        null=True,
        blank=True
    )
    
    # Demographics
    picture_url = models.URLField(max_length=1000, null=True, blank=True) # Customer's Picture
    date_of_birth = models.DateField(null=True, blank=True)
    age = models.IntegerField(null=True, blank=True)  # Can be calculated from DOB
    gender_id = models.ForeignKey(
        to='Gender', 
        on_delete=models.SET_NULL, 
        related_name='customer_gender',
        blank=True,
        null=True
    )
    nationality = models.CharField(max_length=100, null=True, blank=True)
    
    # Identification
    national_id = models.CharField(
        max_length=20, 
        null=True, 
        blank=True,
        # validators=[RegexValidator(regex=r'^\d{13}$', message='Thai ID must be 13 digits')]
    )
    passport_number = models.CharField(max_length=20, null=True, blank=True)
    tax_id = models.CharField(max_length=20, null=True, blank=True)
    
    # ========== CONTACT INFORMATION ==========
    # Primary contact
    email = models.EmailField(null=True, blank=True, db_index=True)
    email_verified = models.BooleanField(default=False)
    email_verified_date = models.DateTimeField(null=True, blank=True)
    
    phone = models.CharField(
        max_length=20, 
        null=True, 
        blank=True,
        # validators=[RegexValidator(regex=r'^\+?\d{9,15}$', message='Enter valid phone number')],
        db_index=True
    )
    phone_verified = models.BooleanField(default=False)
    phone_verified_date = models.DateTimeField(null=True, blank=True)
    
    # Alternative contacts
    alternative_email = models.EmailField(null=True, blank=True)
    alternative_phone = models.CharField(max_length=20, null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=100, null=True, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, null=True, blank=True)
    
    # ========== ADDRESS INFORMATION ==========
    # Current address
    address = models.JSONField(default=dict, null=True, blank=True, help_text="Store address as a JSON object") # address_line1, address_line2, city, state_province_region, zip_code, country
    # address_line1 = models.CharField(max_length=255, null=True, blank=True)
    # address_line2 = models.CharField(max_length=255, null=True, blank=True)
    subdistrict = models.CharField(max_length=100, null=True, blank=True)
    district = models.CharField(max_length=100, null=True, blank=True)
    province = models.CharField(max_length=100, null=True, blank=True)
    postal_code = models.CharField(max_length=10, null=True, blank=True)
    country = models.CharField(max_length=100, default='Thailand', null=True, blank=True)
    
    # ========== PROFESSIONAL INFORMATION ==========
    career = models.CharField(max_length=100, null=True, blank=True)
    occupation = models.CharField(max_length=100, null=True, blank=True)
    company_name = models.CharField(max_length=255, null=True, blank=True)
    industry = models.CharField(max_length=100, null=True, blank=True)
    annual_income_range = models.CharField(
        max_length=50,
        choices=[
            ('0-300K', '0 - 300,000 THB'),
            ('300K-500K', '300,001 - 500,000 THB'),
            ('500K-1M', '500,001 - 1,000,000 THB'),
            ('1M-3M', '1,000,001 - 3,000,000 THB'),
            ('3M+', 'More than 3,000,000 THB'),
        ],
        null=True,
        blank=True
    )
    
    # ========== PREFERENCES ==========
    preferred_language = models.CharField(
        max_length=10,
        choices=[
            ('th', 'Thai'),
            ('en', 'English'),
            ('zh', 'Chinese'),
            ('ja', 'Japanese'),
        ],
        default='th'
    )
    preferred_contact_method = models.CharField(
        max_length=20,
        choices=[
            ('EMAIL', 'Email'),
            ('PHONE', 'Phone Call'),
            ('SMS', 'SMS'),
            ('LINE', 'LINE'),
            # ('WHATSAPP', 'WhatsApp'),
        ],
        null=True,
        blank=True
    )
    preferred_contact_time = models.CharField(
        max_length=20,
        choices=[
            ('MORNING', '09:00 - 12:00'),
            ('AFTERNOON', '12:00 - 17:00'),
            ('EVENING', '17:00 - 20:00'),
            ('ANYTIME', 'Anytime'),
        ],
        default='ANYTIME'
    )
    
    # Communication preferences
    accepts_marketing = models.BooleanField(default=True)
    accepts_sms = models.BooleanField(default=True)
    accepts_email = models.BooleanField(default=True)
    accepts_push_notifications = models.BooleanField(default=True)
    
    # ========== CUSTOMER RELATIONSHIP ==========
    CUSTOMER_TYPE_CHOICES = [
        ('CUSTOMER', 'Customer'),
        ('AGENT', 'Agent'),
        ('BROKER', 'Broker'),
        ('PROSPECT', 'Prospect'),
        ('NEW', 'New Customer'),
        ('REGULAR', 'Regular Customer'),
        ('VIP', 'VIP Customer'),
        ('INACTIVE', 'Inactive Customer'),
    ]

    # Customer value
    customer_type = models.CharField(
        max_length=20,
        choices=CUSTOMER_TYPE_CHOICES,
        default='CUSTOMER'
    )
    customer_segment = models.CharField(max_length=50, null=True, blank=True)
    lifetime_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    
    # Referral information
    referral_source = models.CharField(
        max_length=50,
        choices=[
            ('DIRECT', 'Direct'),
            ('SOCIAL_MEDIA', 'Social Media'),
            ('REFERRAL', 'Customer Referral'),
            ('AGENT', 'Agent Referral'),
            ('MARKETING', 'Marketing Campaign'),
            ('PARTNER', 'Partner'),
            ('OTHER', 'Other'),
        ],
        null=True,
        blank=True
    )
    referred_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='referrals'
    )
    referral_code = models.CharField(max_length=20, unique=True, null=True, blank=True)
    
    # ========== ACCOUNT STATUS ==========
    account_status = models.CharField(
        max_length=20,
        choices=[
            ('ACTIVE', 'Active'),
            ('INACTIVE', 'Inactive'),
            ('SUSPENDED', 'Suspended'),
            ('BLACKLISTED', 'Blacklisted'),
            ('DELETED', 'Deleted'),
        ],
        default='ACTIVE'
    )
    
    # Risk and compliance
    risk_level = models.CharField(
        max_length=20,
        choices=[
            ('LOW', 'Low Risk'),
            ('MEDIUM', 'Medium Risk'),
            ('HIGH', 'High Risk'),
        ],
        default='LOW'
    )
    kyc_status = models.CharField(
        max_length=20,
        choices=[
            ('NOT_STARTED', 'Not Started'),
            ('PENDING', 'Pending'),
            ('VERIFIED', 'Verified'),
            ('REJECTED', 'Rejected'),
        ],
        default='NOT_STARTED'
    )
    kyc_verified_date = models.DateTimeField(null=True, blank=True)
    
    # ========== ACTIVITY TRACKING ==========
    first_contact_date = models.DateTimeField(null=True, blank=True)
    last_contact_date = models.DateTimeField(null=True, blank=True)
    last_purchase_date = models.DateTimeField(null=True, blank=True)
    total_purchases = models.IntegerField(default=0)
    total_spent = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # ========== PLATFORM SPECIFIC ==========
    main_interface_id = models.ForeignKey(
        to='Interface',
        on_delete=models.SET_NULL, 
        related_name='customer_interface',
        blank=True,
        null=True
    )
    
    # Tags and categorization
    customer_tags = models.ManyToManyField(
        'CustomerTag',
        related_name='customers',
        blank=True
    )
    
    # ========== ACCOUNT LINKING ==========
    linking_code = models.CharField(max_length=10, null=True, blank=True)
    linking_code_expires = models.DateTimeField(null=True, blank=True)
    
    # ========== GDPR/PRIVACY ==========
    consent_data_processing = models.BooleanField(default=False)
    consent_data_processing_date = models.DateTimeField(null=True, blank=True)
    data_retention_period = models.IntegerField(
        default=365,  # days
        help_text="Number of days to retain customer data"
    )
    deletion_requested = models.BooleanField(default=False)
    deletion_requested_date = models.DateTimeField(null=True, blank=True)
    
    # ========== METADATA ==========
    notes = models.TextField(blank=True, null=True)
    custom_fields = models.JSONField(
        default=dict,
        blank=True,
        help_text="Store any additional custom fields"
    )
    
    # ========== AUDIT FIELDS ==========
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customer_created_by',
        blank=True, 
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customer_updated_by',
        blank=True, 
        null=True
    )
    updated_on = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
            models.Index(fields=['national_id']),
            models.Index(fields=['customer_type']),
            models.Index(fields=['account_status']),
            models.Index(fields=['created_on']),
            models.Index(fields=['last_contact_date']),
        ]
    
    def calculate_age(self):
        """Calculate age from date of birth"""
        if self.date_of_birth:
            today = timezone.now().date()
            age = today.year - self.date_of_birth.year
            if today.month < self.date_of_birth.month or \
               (today.month == self.date_of_birth.month and today.day < self.date_of_birth.day):
                age -= 1
            self.age = age
            self.save(update_fields=['age'])
            return age
        return None
    
    def get_full_name(self):
        """Get formatted full name"""
        if self.first_name and self.last_name:
            return f"{self.title or ''} {self.first_name} {self.last_name}".strip()
        return self.name or f"Customer {self.customer_id}"
    
    def generate_referral_code(self):
        """Generate unique referral code"""
        if not self.referral_code:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            while Customer.objects.filter(referral_code=code).exists():
                code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
            self.referral_code = code
            self.save(update_fields=['referral_code'])
        return self.referral_code

    def generate_linking_code(self):
        """Generate a unique linking code for account linking"""
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        self.linking_code = code
        self.linking_code_expires = timezone.now() + timezone.timedelta(hours=24)
        self.save()
        return code

    def verify_linking_code(self, code):
        """Verify if the provided linking code is valid"""
        if not self.linking_code or not self.linking_code_expires:
            return False
        if self.linking_code != code:
            return False
        if timezone.now() > self.linking_code_expires:
            return False
        return True

    def get_platform_identities(self):
        """Get all platform identities for this customer"""
        return self.platform_identities.filter(is_active=True)

    def get_identity_for_platform(self, platform, provider_id=None):
        """Get specific platform identity"""
        identities = self.platform_identities.filter(
            platform=platform,
            is_active=True
        )
        if provider_id:
            identities = identities.filter(provider_id=provider_id)
        return identities.first()

    @property
    def blob_folder(self):
        """Returns the customer's blob storage folder path"""
        return f"customer/{self.customer_id}/"

    def upload_file(self, file, filename=None):
        """Upload a file to the customer's folder in Azure Blob Storage"""
        azure_storage = AzureBlobStorage()
        if filename is None:
            filename = file.name
        
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.upload_file(file, blob_name)

    def delete_file(self, filename):
        """Delete a file from the customer's folder"""
        azure_storage = AzureBlobStorage()
        blob_name = f"{self.blob_folder}{filename}"
        return azure_storage.delete_file(blob_name)

    def list_files(self):
        """List all files in the customer's folder"""
        azure_storage = AzureBlobStorage()
        return azure_storage.list_files(self.blob_folder)

    def __str__(self):
         
        if self.get_full_name():
            str_name = self.get_full_name()
        elif self.name:
            str_name = self.name
        else:
            str_name = "Unnamed"
        national_id = self.national_id if self.national_id else None
        # return f"{self.customer_id}: {self.name or 'Unnamed'}"
        return f"CUSTOMER ID: {self.customer_id}, NAME: {str_name}, NATIONAL ID: {national_id}"

class CustomerNote(models.Model):
    customer = models.ForeignKey(
        to=Customer,
        on_delete=models.CASCADE,
        related_name='customer_note_customer'
    )
    # content = models.TextField(blank=True, null=True)
    content = models.TextField()
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customer_note_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customer_note_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Note for {self.customer} by {self.created_by}"
    

class CustomerMemory(models.Model):
    customer = models.ForeignKey(
        to='customer.Customer',
        on_delete=models.CASCADE,
        related_name='customer_memories'
    )
    entity_one = models.CharField(max_length=255)
    entity_two = models.CharField(max_length=255)
    relation_type = models.CharField(max_length=100)
    is_important = models.BooleanField(default=False)
    detail_en = models.TextField()
    detail_th = models.TextField()
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customer_memories_created_by',
        blank=True, 
        null=True
        )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='customer_memories_updated_by',
        blank=True, 
        null=True
        )
    updated_on = models.DateTimeField(auto_now=True)
    ticket = models.ForeignKey(
        to='ticket.Ticket',
        on_delete=models.SET_NULL,
        null=True,
        related_name='generated_memories'
    )
    
    class Meta:
        verbose_name = "Customer Memory"
        verbose_name_plural = "Customer Memories"
        indexes = [
            models.Index(fields=['customer']),
            models.Index(fields=['customer', 'is_important']),
            models.Index(fields=['entity_one']),
            models.Index(fields=['entity_two']),
            models.Index(fields=['relation_type']),
        ]
    
    def __str__(self):
        return f"Customer ID:{self.customer.customer_id}, Ticket ID: {self.ticket.id} || {self.entity_one} - {self.relation_type} - {self.entity_two}"
    
class CustomerPlatformIdentity(models.Model):
    """
    Stores platform-specific identities for customers.
    Handles multi-provider scenarios like LINE's different UUIDs per provider.
    """
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='platform_identities'
    )
    
    # Platform information
    platform = models.CharField(
        max_length=20,
        choices=[
            ('LINE', 'LINE'),
            ('WHATSAPP', 'WhatsApp'),
            ('FACEBOOK', 'Facebook Messenger'),
            ('TELEGRAM', 'Telegram'),
            ('INSTAGRAM', 'Instagram'),
        ]
    )
    
    # Platform-specific user ID
    platform_user_id = models.CharField(max_length=255)
    
    # Provider/Channel information (for platforms like LINE with multiple providers)
    provider_id = models.CharField(max_length=100, null=True, blank=True)
    provider_name = models.CharField(max_length=255, null=True, blank=True)
    channel_id = models.CharField(max_length=100, null=True, blank=True)
    channel_name = models.CharField(max_length=255, null=True, blank=True)
    
    # Platform-specific profile data
    display_name = models.CharField(max_length=255, null=True, blank=True)
    picture_url = models.URLField(max_length=1000, null=True, blank=True)
    status_message = models.TextField(null=True, blank=True)
    
    # Additional platform data (JSON field for flexibility)
    platform_data = models.JSONField(default=dict, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    last_interaction = models.DateTimeField(null=True, blank=True)

    # LINE
    # Rich Menu tracking fields
    current_line_rich_menu_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text='Currently assigned LINE rich menu ID'
    )
    
    line_rich_menu_updated_on = models.DateTimeField(
        null=True,
        blank=True,
        help_text='Last time rich menu was updated'
    )
    
    # Audit fields
    created_by = models.ForeignKey(
        to=settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='platform_identity_created_by',
        blank=True,
        null=True
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['platform', 'platform_user_id', 'provider_id', 'channel_id']
        indexes = [
            models.Index(fields=['platform', 'platform_user_id']),
            models.Index(fields=['customer', 'platform']),
            models.Index(fields=['provider_id']),
        ]

    def __str__(self):
        provider_info = f" ({self.provider_name or self.provider_id})" if self.provider_id else ""
        channel_info = f" [{self.channel_name}]" if self.channel_name else ""
        return f"ID: {self.id} {self.platform}{provider_info}{channel_info}: {self.display_name or self.platform_user_id}"


class CustomerLinkingHistory(models.Model):
    """
    Tracks account linking attempts and history for audit purposes.
    """
    primary_customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='linking_history_primary'
    )
    linked_customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='linking_history_linked',
        null=True,
        blank=True
    )
    
    # Linking method
    linking_method = models.CharField(
        max_length=20,
        choices=[
            ('CODE', 'Linking Code'),
            ('EMAIL', 'Email Verification'),
            ('PHONE', 'Phone Verification'),
            ('MANUAL', 'Manual by Staff'),
            ('AUTO', 'Automatic Match'),
        ]
    )
    
    # Platform identity that initiated the linking
    platform_identity = models.ForeignKey(
        CustomerPlatformIdentity,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('SUCCESS', 'Success'),
            ('FAILED', 'Failed'),
            ('EXPIRED', 'Expired'),
        ]
    )
    
    # Additional data
    linking_code_used = models.CharField(max_length=10, null=True, blank=True)
    failure_reason = models.CharField(max_length=255, null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)
    
    created_on = models.DateTimeField(auto_now_add=True)
    completed_on = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_on']

    def __str__(self):
        return f"Linking {self.primary_customer} via {self.linking_method} - {self.status}"


class CustomerPolicyWorkflowCache(models.Model):
    """Cache for policy workflow results to improve performance"""

    WORKFLOW_TYPE_CHOICES = [
        ('POLICY_LIST', 'Policy List'),
        ('POLICY_DETAILS', 'Policy Details')
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='policy_cache')
    platform_identity = models.ForeignKey(
        CustomerPlatformIdentity,
        on_delete=models.CASCADE,
        related_name='policy_cache',
        null=True,
        blank=True,
        help_text='Platform identity used for this cache entry'
    )
    workflow_type = models.CharField(max_length=20, choices=WORKFLOW_TYPE_CHOICES)
    member_code = models.CharField(max_length=100, null=True, blank=True)  # For policy details
    citizen_id = models.CharField(max_length=20)
    social_id = models.CharField(max_length=100)
    channel_id = models.CharField(max_length=100)
    channel = models.CharField(max_length=20)
    raw_response_data = models.JSONField()  # Raw TPA API response
    processed_data = models.JSONField()  # Processed data for frontend
    execution_id = models.CharField(max_length=50, unique=True)
    execution_time_ms = models.IntegerField()  # Execution time in milliseconds
    success = models.BooleanField(default=True)
    error_message = models.TextField(null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()  # Cache expiration time

    class Meta:
        unique_together = ['customer', 'workflow_type', 'member_code', 'platform_identity']
        indexes = [
            models.Index(fields=['customer', 'workflow_type']),
            models.Index(fields=['customer', 'member_code']),
            models.Index(fields=['platform_identity']),
            models.Index(fields=['customer', 'platform_identity', 'workflow_type']),
            models.Index(fields=['execution_id']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        platform_info = f" (Platform: {self.platform_identity.pk})" if self.platform_identity else ""
        return f"Cache {self.workflow_type} for {self.customer}{platform_info} - {self.execution_id}"


class CustomerPolicyWorkflowAuditLog(models.Model):
    """Comprehensive audit log for policy workflow executions"""

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    requested_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    workflow_type = models.CharField(max_length=20)
    execution_id = models.CharField(max_length=50)
    step_results = models.JSONField()  # Results from each workflow step
    total_execution_time_ms = models.IntegerField()
    success = models.BooleanField()
    error_details = models.JSONField(null=True)  # Error information if failed
    tpa_calls_made = models.IntegerField(default=0)  # Number of TPA API calls
    tpa_total_time_ms = models.IntegerField(default=0)  # Total time spent on TPA calls
    created_on = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['customer', 'created_on']),
            models.Index(fields=['execution_id']),
            models.Index(fields=['workflow_type', 'created_on']),
        ]

    def __str__(self):
        return f"Audit {self.workflow_type} for {self.customer} - {self.execution_id}"
