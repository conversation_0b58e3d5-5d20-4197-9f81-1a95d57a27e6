# Generated manually for policy workflow migration
# Combined with platform_identity field updates from 0010_update_policy_cache_platform_identity

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer', '0008_customerplatformidentity_current_line_rich_menu_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerPolicyWorkflowCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('workflow_type', models.CharField(choices=[('POLICY_LIST', 'Policy List'), ('POLICY_DETAILS', 'Policy Details')], max_length=20)),
                ('member_code', models.CharField(blank=True, max_length=100, null=True)),
                ('citizen_id', models.Char<PERSON><PERSON>(max_length=20)),
                ('social_id', models.Char<PERSON>ield(max_length=100)),
                ('channel_id', models.CharField(max_length=100)),
                ('channel', models.CharField(max_length=20)),
                ('raw_response_data', models.JSONField()),
                ('processed_data', models.J<PERSON>NField()),
                ('execution_id', models.CharField(max_length=50, unique=True)),
                ('execution_time_ms', models.IntegerField()),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='policy_cache', to='customer.customer')),
                ('platform_identity', models.ForeignKey(
                    null=True,
                    blank=True,
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='policy_cache',
                    to='customer.customerplatformidentity',
                    help_text='Platform identity used for this cache entry'
                )),
            ],
            options={
                'indexes': [
                    models.Index(fields=['customer', 'workflow_type'], name='customer_cu_custome_113caa_idx'),
                    models.Index(fields=['customer', 'member_code'], name='customer_cu_custome_92159f_idx'),
                    models.Index(fields=['execution_id'], name='customer_cu_executi_92305e_idx'),
                    models.Index(fields=['expires_at'], name='customer_cu_expires_ae2781_idx'),
                    models.Index(fields=['platform_identity'], name='customer_cu_platfor_ff6f1f_idx'),
                    models.Index(fields=['customer', 'platform_identity', 'workflow_type'], name='customer_cu_custome_f38d64_idx'),
                ],
                'unique_together': {('customer', 'workflow_type', 'member_code', 'platform_identity')},
            },
        ),
        migrations.CreateModel(
            name='CustomerPolicyWorkflowAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('workflow_type', models.CharField(max_length=20)),
                ('execution_id', models.CharField(max_length=50)),
                ('step_results', models.JSONField()),
                ('total_execution_time_ms', models.IntegerField()),
                ('success', models.BooleanField()),
                ('error_details', models.JSONField(null=True)),
                ('tpa_calls_made', models.IntegerField(default=0)),
                ('tpa_total_time_ms', models.IntegerField(default=0)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customer')),
                ('requested_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [
                    models.Index(fields=['customer', 'created_on'], name='customer_cu_custome_8789cb_idx'),
                    models.Index(fields=['execution_id'], name='customer_cu_executi_d86f2f_idx'),
                    models.Index(fields=['workflow_type', 'created_on'], name='customer_cu_workflo_572a03_idx'),
                ],
            },
        ),
    ]
