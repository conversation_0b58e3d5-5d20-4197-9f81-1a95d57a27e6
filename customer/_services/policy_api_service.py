import requests
import time
import logging
import json
from typing import Dict, Any, Optional

from .policy_workflow_config_manager import get_workflow_configuration_manager

logger = logging.getLogger('django.customer_policy_crm')

class TPAApiService:
    """
    Enhanced TPA (Third Party Administrator) API integration service.

    This service provides a unified interface for interacting with the BVTPA API,
    using configuration-driven approach instead of hardcoded constants.
    Supports V2 workflow configuration format with environment-specific settings.
    """

    # Content types (keep as constants since they're HTTP standards)
    CONTENT_TYPE_JSON = "application/json"
    CONTENT_TYPE_FORM_URLENCODED = "application/x-www-form-urlencoded"

    # HTTP methods (keep as constants since they're HTTP standards)
    HTTP_METHOD_POST = "POST"

    def __init__(self, workflow_id: str = 'bvtpa_policy_list'):
        logger.info(f"TPAApiService.__init__: Initializing enhanced TPA API service for workflow: {workflow_id}")

        # Initialize configuration manager and load workflow config
        self.config_manager = get_workflow_configuration_manager()
        self.workflow_config = self.config_manager.load_workflow_config(workflow_id)
        self.api_config = self.config_manager.get_api_config(self.workflow_config)
        self.credentials = self.config_manager.get_credentials(self.workflow_config)

        # Initialize session with configuration-driven settings
        self.session = requests.Session()
        self.session.verify = self.api_config.ssl_verify

        logger.info(f"TPAApiService.__init__: Configuration loaded - base_url={self.api_config.base_url}, timeout={self.api_config.timeout_seconds}s, ssl_verify={self.api_config.ssl_verify}")

        if not self.api_config.ssl_verify:
            logger.warning("TPAApiService.__init__: SSL verification is disabled. This should only be used in development/testing.")
            # Disable SSL warnings when verification is disabled
            try:
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                logger.info("TPAApiService.__init__: SSL warnings disabled successfully")
            except ImportError:
                logger.warning("TPAApiService.__init__: urllib3 not available, SSL warnings cannot be disabled")

        logger.info("TPAApiService.__init__: Enhanced TPA API service initialization completed successfully")

    def get_endpoint_url(self, endpoint_key: str) -> str:
        """Get full URL for an endpoint using configuration"""
        endpoint_path = self.api_config.endpoints.get(endpoint_key, f"/api/{endpoint_key}")
        return f"{self.api_config.base_url}{endpoint_path}"

    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with retry logic"""
        # Add default timeout if not specified
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.api_config.timeout_seconds

        # Debug logging
        logger.info(f"TPAApiService._make_request_with_retry: Starting request - Method: {method}, URL: {url}")
        logger.debug(f"TPAApiService._make_request_with_retry: Session verify: {self.session.verify}")
        logger.debug(f"TPAApiService._make_request_with_retry: Request timeout: {kwargs.get('timeout')}")

        # Log request headers (excluding sensitive data)
        headers = kwargs.get('headers', {})
        safe_headers = {k: ('***' if 'authorization' in k.lower() else v) for k, v in headers.items()}
        logger.debug(f"TPAApiService._make_request_with_retry: Request headers: {safe_headers}")

        last_exception = None
        max_attempts = self.api_config.max_retries
        for attempt in range(max_attempts):
            try:
                logger.debug(f"TPAApiService._make_request_with_retry: Attempt {attempt + 1}/{max_attempts}")
                start_time = time.time()

                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()

                request_time = (time.time() - start_time) * 1000
                logger.info(f"TPAApiService._make_request_with_retry: Request successful on attempt {attempt + 1} - Status: {response.status_code}, Time: {request_time:.2f}ms")
                logger.debug(f"TPAApiService._make_request_with_retry: Response headers: {dict(response.headers)}")

                return response
            except requests.exceptions.RequestException as e:
                last_exception = e
                request_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
                logger.warning(f"TPAApiService._make_request_with_retry: Request failed (attempt {attempt + 1}/{max_attempts}) after {request_time:.2f}ms: {str(e)}")

                if attempt < max_attempts - 1:
                    delay = self.api_config.retry_delay_seconds
                    if self.api_config.backoff_strategy == 'exponential':
                        backoff_delay = delay * (2 ** attempt)
                    else:
                        backoff_delay = delay
                    logger.debug(f"TPAApiService._make_request_with_retry: Waiting {backoff_delay}s before retry")
                    time.sleep(backoff_delay)  # Exponential backoff

        # If we get here, all retries failed
        logger.error(f"TPAApiService._make_request_with_retry: All {max_attempts} attempts failed for {method} {url}. Final error: {str(last_exception)}")
        raise last_exception or requests.exceptions.RequestException("All retry attempts failed")
    
    def get_bearer_token(self, social_id: str, channel_id: str, channel: str) -> str:
        """Get bearer token from TPA API using configuration"""
        url = self.get_endpoint_url('get_token')
        logger.info(f"TPAApiService.get_bearer_token: Requesting bearer token for social_id={social_id}, channel_id={channel_id}, channel={channel}")

        payload = {
            "USERNAME": self.credentials.get('username'),
            "PASSWORD": self.credentials.get('password'),
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }

        # Log payload without sensitive data
        safe_payload = {k: ('***' if k in ['PASSWORD'] else v) for k, v in payload.items()}
        logger.debug(f"TPAApiService.get_bearer_token: Request payload: {safe_payload}")

        headers = {
            "Content-Type": self.CONTENT_TYPE_FORM_URLENCODED
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, data=payload, headers=headers)
            token_data = response.text.strip()
            logger.info(f"TPAApiService.get_bearer_token: Successfully obtained bearer token for social_id={social_id}")
            logger.debug(f"TPAApiService.get_bearer_token: Token length: {len(token_data)} characters")
            return token_data
        except requests.exceptions.RequestException as e:
            logger.error(f"TPAApiService.get_bearer_token: HTTP request failed for social_id={social_id}: {str(e)}")
            raise Exception(f"TPA authentication request failed: {str(e)}")
        except Exception as e:
            logger.error(f"TPAApiService.get_bearer_token: Unexpected error getting bearer token for social_id={social_id}: {str(e)}")
            raise Exception(f"TPA authentication failed: {str(e)}")
    
    def verify_citizen_id(self, token: str, citizen_id: str) -> Dict[str, Any]:
        """Verify citizen ID with TPA API using configuration"""
        url = self.get_endpoint_url('search_citizen_id')
        logger.info(f"TPAApiService.verify_citizen_id: Verifying citizen ID: {citizen_id}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id
        }

        logger.debug(f"TPAApiService.verify_citizen_id: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log full API response for debugging
            logger.info(f"TPAApiService.verify_citizen_id: Full API response for citizen ID {citizen_id}: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            # Log response summary without sensitive data
            if isinstance(response_data, dict):
                response_fields = self.config_manager.get_response_fields(self.workflow_config)
                search_results_field = response_fields.get('citizen_search_results', 'ListOfSearchCitizenID')
                status_field = response_fields.get('status_field', 'Status')

                search_results = response_data.get(search_results_field, [])
                logger.info(f"TPAApiService.verify_citizen_id: Citizen ID verification completed for {citizen_id} - Found {len(search_results)} results")
                if search_results:
                    first_result = search_results[0]
                    status = first_result.get(status_field, 'unknown')
                    logger.debug(f"TPAApiService.verify_citizen_id: First result status: {status}")
                    # Log all fields in first result for debugging
                    logger.debug(f"TPAApiService.verify_citizen_id: First result fields: {list(first_result.keys()) if isinstance(first_result, dict) else 'Not a dict'}")

            return response_data
        except requests.exceptions.RequestException as e:
            logger.error(f"TPAApiService.verify_citizen_id: HTTP request failed for citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification request failed: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"TPAApiService.verify_citizen_id: Invalid JSON response for citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification response parsing failed: {str(e)}")
        except Exception as e:
            logger.error(f"TPAApiService.verify_citizen_id: Unexpected error verifying citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification failed: {str(e)}")
    
    def check_registration(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Check registration status with TPA API using configuration"""
        url = self.get_endpoint_url('check_register')
        logger.info(f"TPAApiService.check_registration: Checking registration for citizen_id={citizen_id}, social_id={social_id}, channel_id={channel_id}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }

        logger.debug(f"TPAApiService.check_registration: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log full API response for debugging
            logger.info(f"TPAApiService.check_registration: Full API response for citizen_id={citizen_id}: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            # Log response summary
            if isinstance(response_data, dict):
                check_results = response_data.get('ListOfCheckRegister', [])
                logger.info(f"TPAApiService.check_registration: Registration check completed for citizen_id={citizen_id} - Found {len(check_results)} results")
                if check_results:
                    first_result = check_results[0]
                    status = first_result.get('Status', 'unknown')
                    logger.debug(f"TPAApiService.check_registration: Registration status: {status}")
                    # Log all fields in first result for debugging
                    logger.debug(f"TPAApiService.check_registration: First result fields: {list(first_result.keys()) if isinstance(first_result, dict) else 'Not a dict'}")

            return response_data
        except requests.exceptions.RequestException as e:
            logger.error(f"TPAApiService.check_registration: HTTP request failed for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check request failed: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"TPAApiService.check_registration: Invalid JSON response for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check response parsing failed: {str(e)}")
        except Exception as e:
            logger.error(f"TPAApiService.check_registration: Unexpected error checking registration for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check failed: {str(e)}")
    
    def get_policy_list(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Get policy list from TPA API using configuration"""
        url = self.get_endpoint_url('policy_list_social')
        logger.info(f"TPAApiService.get_policy_list: Retrieving policy list for citizen_id={citizen_id}, social_id={social_id}, channel_id={channel_id}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }

        logger.debug(f"TPAApiService.get_policy_list: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log full API response for debugging
            logger.info(f"TPAApiService.get_policy_list: Full API response for citizen_id={citizen_id}: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            # Log response summary
            if isinstance(response_data, dict):
                policy_list = response_data.get('ListOfPolicyListSocial', [])
                logger.info(f"TPAApiService.get_policy_list: Policy list retrieval completed for citizen_id={citizen_id} - Found {len(policy_list)} policies")

                # Log member codes found
                member_codes = []
                for policy in policy_list:
                    if isinstance(policy, dict) and 'MemberCode' in policy:
                        member_code = policy['MemberCode']
                        if member_code not in member_codes:
                            member_codes.append(member_code)

                logger.debug(f"TPAApiService.get_policy_list: Found {len(member_codes)} unique member codes: {member_codes}")

            return response_data
        except requests.exceptions.RequestException as e:
            logger.error(f"TPAApiService.get_policy_list: HTTP request failed for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list request failed: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"TPAApiService.get_policy_list: Invalid JSON response for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list response parsing failed: {str(e)}")
        except Exception as e:
            logger.error(f"TPAApiService.get_policy_list: Unexpected error retrieving policy list for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list retrieval failed: {str(e)}")
    
    def get_policy_details(self, token: str, citizen_id: str, social_id: str, channel_id: str, member_code: str) -> Dict[str, Any]:
        """Get policy details from TPA API using configuration"""
        url = self.get_endpoint_url('policy_detail_social')
        logger.info(f"TPAApiService.get_policy_details: Retrieving policy details for citizen_id={citizen_id}, social_id={social_id}, channel_id={channel_id}, member_code={member_code}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id,
            "MemberCode": member_code
        }

        logger.debug(f"TPAApiService.get_policy_details: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log full API response for debugging
            # logger.info(f"TPAApiService.get_policy_details: Full API response for member_code={member_code}: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            # Log partial API response for debugging
            logger.info(f"TPAApiService.get_policy_details: Partial API response for member_code={member_code}: {json.dumps(response_data, indent=2, ensure_ascii=False)[:100]}...")

            # Log response summary
            if isinstance(response_data, dict):
                policy_details = response_data.get('ListOfPolDet', [])
                policy_claims = response_data.get('ListOfPolClaim', [])
                logger.info(f"TPAApiService.get_policy_details: Policy details retrieval completed for member_code={member_code} - Found {len(policy_details)} policy details and {len(policy_claims)} claims")

            return response_data
        except requests.exceptions.RequestException as e:
            logger.error(f"TPAApiService.get_policy_details: HTTP request failed for member {member_code}: {str(e)}")
            raise Exception(f"Policy details request failed: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"TPAApiService.get_policy_details: Invalid JSON response for member {member_code}: {str(e)}")
            raise Exception(f"Policy details response parsing failed: {str(e)}")
        except Exception as e:
            logger.error(f"TPAApiService.get_policy_details: Unexpected error retrieving policy details for member {member_code}: {str(e)}")
            raise Exception(f"Policy details retrieval failed: {str(e)}")

    # Dynamic request methods for JSON-driven workflows
    def make_dynamic_request(self, endpoint: str, method: str, payload: Dict[str, Any],
                           headers: Optional[Dict[str, str]] = None) -> Any:
        """Make a dynamic API request with configurable payload and headers"""
        url = f"{self.api_config.base_url}{endpoint}"
        logger.info(f"TPAApiService.make_dynamic_request: Making dynamic request - endpoint={endpoint}, method={method}")

        # Default headers
        request_headers = {
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        # Add custom headers if provided
        if headers:
            request_headers.update(headers)

        # Log request details (excluding sensitive data)
        safe_payload = {k: ('***' if k in ['PASSWORD'] else v) for k, v in payload.items()}
        safe_headers = {k: ('***' if 'authorization' in k.lower() else v) for k, v in request_headers.items()}
        logger.debug(f"TPAApiService.make_dynamic_request: Request payload: {safe_payload}")
        logger.debug(f"TPAApiService.make_dynamic_request: Request headers: {safe_headers}")

        try:
            # Determine request method and payload format
            if method.upper() == self.HTTP_METHOD_POST:
                if request_headers.get("Content-Type") == self.CONTENT_TYPE_FORM_URLENCODED:
                    logger.debug(f"TPAApiService.make_dynamic_request: Using form-urlencoded payload")
                    response = self._make_request_with_retry(method, url, data=payload, headers=request_headers)
                else:
                    logger.debug(f"TPAApiService.make_dynamic_request: Using JSON payload")
                    response = self._make_request_with_retry(method, url, json=payload, headers=request_headers)
            else:
                logger.debug(f"TPAApiService.make_dynamic_request: Using query parameters")
                response = self._make_request_with_retry(method, url, params=payload, headers=request_headers)

            # Handle different response types
            content_type = response.headers.get('content-type', '').lower()
            if self.CONTENT_TYPE_JSON in content_type:
                logger.debug(f"TPAApiService.make_dynamic_request: Parsing JSON response")
                response_data = response.json()

                # Log full API response for debugging
                # logger.info(f"TPAApiService.make_dynamic_request: Full API response for endpoint {endpoint}: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

                # Log partial API response for debugging
                logger.info(f"TPAApiService.make_dynamic_request: Partial API response for endpoint {endpoint}: {json.dumps(response_data, indent=2, ensure_ascii=False)[:500]}...")

                logger.info(f"TPAApiService.make_dynamic_request: Dynamic request completed successfully - endpoint={endpoint}, response_type=json")
                return response_data
            else:
                # For token endpoint which returns plain text
                logger.debug(f"TPAApiService.make_dynamic_request: Parsing text response")
                response_text = response.text.strip().replace('"', '')

                # Log full API response for debugging (token responses are usually short)
                logger.info(f"TPAApiService.make_dynamic_request: Full API response for endpoint {endpoint}: {response_text}")

                logger.info(f"TPAApiService.make_dynamic_request: Dynamic request completed successfully - endpoint={endpoint}, response_type=text, length={len(response_text)}")
                return response_text

        except requests.exceptions.RequestException as e:
            logger.error(f"TPAApiService.make_dynamic_request: HTTP request failed for {endpoint}: {str(e)}")
            raise Exception(f"API request to {endpoint} failed: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"TPAApiService.make_dynamic_request: Invalid JSON response from {endpoint}: {str(e)}")
            raise Exception(f"API response parsing failed for {endpoint}: {str(e)}")
        except Exception as e:
            logger.error(f"TPAApiService.make_dynamic_request: Unexpected error for {endpoint}: {str(e)}")
            raise Exception(f"API request to {endpoint} failed: {str(e)}")


