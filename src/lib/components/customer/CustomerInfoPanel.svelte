<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { Customer } from '$lib/types/customer';
	import InformationTab from './tabs/InformationTab.svelte';
	import RequestSummaryTab from './tabs/RequestSummaryTab.svelte';
	import TimelineTab from './tabs/TimelineTab.svelte';
	import PoliciesTab from './tabs/PoliciesTab.svelte';
	import AIGuidanceTab from './tabs/AIGuidanceTab.svelte';
	import { services } from '$lib/api/features';
	import { onMount } from 'svelte';

	export let customer: Customer;
	export let customerId: number;
	export let platformId: number | null = null;
	export let access_token: string = '';
	export let hideAITab: boolean = false;
	export let ticketId: number | null = null;
	export let hideEditButton: boolean = false;
	export let hideAssignmentsHistory: boolean = false;
	export let disablePolling: boolean = false;

	let activeTab = 'information';
	let hasPoliciesAccess = false;
	let policiesAccessLoading = true;
	let policiesAccessError: string | null = null;

	// Check feature access on component mount
	onMount(async () => {
		if (access_token) {
			try {
				policiesAccessLoading = true;
				policiesAccessError = null;
				
				// Check if user has access to CRM integration in subscription
				const response = await services.subscription.checkFeatureAccess('crm_integration', access_token);

				if (response.res_status === 200 && response.data) {
					hasPoliciesAccess = response.data.has_access;
					console.log('CustomerInfoPanel: crm_integration access check through subscription:', response);
				} else {
					// If there's an error, default to false and log the error
					hasPoliciesAccess = false;
					policiesAccessError = response.error_msg || 'Failed to check feature access';
					console.warn('CustomerInfoPanel: Failed to check CRM integration access:', response.error_msg);
				}
			} catch (error) {
				hasPoliciesAccess = false;
				policiesAccessError = error instanceof Error ? error.message : 'Unknown error occurred';
				console.error('CustomerInfoPanel: Error checking CRM integration access:', error);
			} finally {
				policiesAccessLoading = false;
			}
		} else {
			// No access token available, default to false
			hasPoliciesAccess = false;
			policiesAccessLoading = false;
			console.warn('CustomerInfoPanel: No access token available for feature access check');
		}
	});

	// Define tab type
	type TabItem = { id: string; label: string; key: string; component: any };

	// Reactive variable for tabs
	let tabs: TabItem[] = [];

	const baseTabs: TabItem[] = [
		{ id: 'information', label: 'Information', key: 'information', component: InformationTab },
		{ id: 'request', label: 'Summary', key: 'summary', component: RequestSummaryTab },
		// { id: 'timeline', label: 'Timeline', key: 'timeline', component: TimelineTab }, //TO DO LATER FOR BVTPA
	];

	const policiesTab: TabItem = { id: 'policies', label: 'Policies', key: 'policies', component: PoliciesTab };
	const aiTab: TabItem = { id: 'ai', label: 'AI Assistant', key: 'ai_assistant', component: AIGuidanceTab };

	// Dynamically build tabs based on access permissions and props
	$: {
		let dynamicTabs: TabItem[] = [...baseTabs];

		// Add policies tab only if user has access and it's not loading
		if (!policiesAccessLoading && hasPoliciesAccess) {
			// Insert policies tab after information tab (index 1)
			dynamicTabs.splice(1, 0, policiesTab);
		}

		// Add AI tab if not hidden
		if (!hideAITab) {
			dynamicTabs.push(aiTab);
		}

		tabs = dynamicTabs;

		// If the current active tab is policies but user doesn't have access, switch to information tab
		if (activeTab === 'policies' && (!hasPoliciesAccess || policiesAccessLoading)) {
			activeTab = 'information';
		}
	}
</script>

<div id="customer-info-customer-info-panel" class="h-full w-full flex flex-col" data-testid="customer-info-panel">
	<!-- Tab Navigation -->
	<div id="customer-info-customer-info-tabs-container" class="bg-white border-b border-gray-200 flex-shrink-0">
		<nav id="customer-info-customer-info-tabs" class="flex w-full">
			{#each tabs as tab}
				<button
					id="customer-info-customer-tab-{tab.id}"
					on:click={() => activeTab = tab.id}
					class="flex-1 px-4 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap text-center
						{activeTab === tab.id
							? 'border-black text-black bg-white'
							: 'border-transparent text-gray-500 hover:text-gray-700'}"
					data-testid="customer-tab-{tab.id}"
					aria-selected={activeTab === tab.id}
					role="tab"
				>
					<!-- {tab.label} -->
					{t(tab.key)}
				</button>
			{/each}
		</nav>
	</div>

	<!-- Tab Content -->
	<div id="customer-info-customer-info-content" class="flex-1 overflow-y-auto bg-gray-50 w-full" data-testid="customer-info-content">
		<div class="w-full h-full">
			{#each tabs as tab}
				{#if activeTab === tab.id}
					<div id="customer-info-customer-tab-content-{tab.id}" data-testid="customer-tab-content-{tab.id}" role="tabpanel" aria-labelledby="customer-info-customer-tab-{tab.id}">
						<svelte:component this={tab.component} {customer} {customerId} {access_token} {platformId} {ticketId} {hideEditButton} {hideAssignmentsHistory} {disablePolling} />
					</div>
				{/if}
			{/each}
		</div>
	</div>
</div>