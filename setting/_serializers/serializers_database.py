import json
from rest_framework import serializers
from setting.models import ChatbotProfile, ConversationFlow#, ConnectedFlow, ConversationFlow
from user.models import UserSchedule



class ChatbotProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatbotProfile
        fields = '__all__'


class ConversationFlowSerializer(serializers.ModelSerializer):
    chatbot = ChatbotProfileSerializer

    class Meta:
        model = ConversationFlow
        fields = [
            'conversationflow_id', 'social_app', 'chatbot',
            'line_channel', 'facebook_channel', 'whatsapp_channel'
        ]

        # fields = ['conversationflow_id', 'social_app', 'line_channel', 'facebook_channel', 'whatsapp_channel','chatbot', 'linkto_flowno']
        
    def validate(self, data):
        social_app = data.get('social_app')
        line_channel = data.get('line_channel')
        facebook_channel = data.get('facebook_channel')
        whatsapp_channel = data.get('whatsapp_channel')

        if social_app:
            # ถ้า social_app ถูกเลือก ให้ช่องอื่นทั้งหมดเป็น null
            if line_channel or facebook_channel or whatsapp_channel:
                raise serializers.ValidationError(
                    "When 'social_app' is set, all channel fields (line, facebook, whatsapp) must be null."
                )
        return data
    
# class ConnectedFlowSerializer(serializers.ModelSerializer):
#     full_message_type = serializers.JSONField(read_only=True)

#     class Meta:
#         model = ConnectedFlow
#         fields = '__all__'